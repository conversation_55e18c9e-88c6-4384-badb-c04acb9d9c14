import { AuthRepository } from '../AuthRepository';
import type { AuthData, UserDetails, TokenMetadata, AdminRolesData } from '@/app/types/auth';

// Mock the storage module
jest.mock('@/lib/storage', () => ({
    getRepositoryStorage: jest.fn()
}));

// Mock logger
jest.mock('@/lib/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
        debug: jest.fn(),
        warn: jest.fn()
    }
}));

describe('AuthRepository Refactored', () => {
    let authRepository: AuthRepository;
    let mockStorage: any;

    // Test data
    const mockUserDetails: UserDetails = {
        id: 123,
        name: 'Test User',
        mobileNumber: '+919876543210'
    };

    const mockTokenMetadata: TokenMetadata = {
        token: 'mock-jwt-token',
        expiresAt: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        refreshAfter: Math.floor(Date.now() / 1000) + 1800 // 30 minutes from now
    };

    const mockAuthData: AuthData = {
        userDetails: mockUserDetails,
        tokenMetadata: mockTokenMetadata
    };

    const mockAdminRoles: AdminRolesData = {
        permissions: {
            manageCategories: true,
            manageSku: false
        },
        roleNames: ['admin', 'category_manager']
    };

    beforeEach(() => {
        // Reset singleton instance
        (AuthRepository as any).instance = undefined;
        
        // Create mock storage
        mockStorage = {
            getItem: jest.fn(),
            setItem: jest.fn(),
            removeItem: jest.fn(),
            clear: jest.fn(),
            keys: jest.fn(),
            length: jest.fn()
        };

        // Mock the getRepositoryStorage function
        const { getRepositoryStorage } = require('@/lib/storage');
        getRepositoryStorage.mockResolvedValue(mockStorage);

        // Reset all mocks
        jest.clearAllMocks();
        
        // Get fresh instance
        authRepository = AuthRepository.getInstance();
    });

    describe('Basic Functionality', () => {
        it('should be a singleton', () => {
            const instance1 = AuthRepository.getInstance();
            const instance2 = AuthRepository.getInstance();
            expect(instance1).toBe(instance2);
        });

        it('should save and retrieve auth data', async () => {
            // Mock successful storage operations
            mockStorage.setItem.mockResolvedValue(undefined);
            mockStorage.getItem.mockResolvedValue(mockAuthData);

            // Save auth data
            await authRepository.saveAuthData(mockAuthData);
            expect(mockStorage.setItem).toHaveBeenCalledWith('user_auth_data', mockAuthData);

            // Retrieve auth data
            const result = await authRepository.getAuthData();
            expect(result).toEqual(mockAuthData);
        });

        it('should handle expired tokens', async () => {
            const expiredAuthData = {
                ...mockAuthData,
                tokenMetadata: {
                    ...mockTokenMetadata,
                    expiresAt: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
                }
            };

            mockStorage.getItem.mockResolvedValue(expiredAuthData);
            mockStorage.removeItem.mockResolvedValue(undefined);

            const result = await authRepository.getAuthData();
            expect(result).toBeNull();
            expect(mockStorage.removeItem).toHaveBeenCalledWith('user_auth_data');
        });

        it('should get valid token when not expired', async () => {
            mockStorage.getItem.mockResolvedValue(mockAuthData);

            const token = await authRepository.getValidToken();
            expect(token).toBe(mockTokenMetadata.token);
        });

        it('should return null for expired token', async () => {
            const expiredAuthData = {
                ...mockAuthData,
                tokenMetadata: {
                    ...mockTokenMetadata,
                    expiresAt: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago
                }
            };

            mockStorage.getItem.mockResolvedValue(expiredAuthData);
            mockStorage.removeItem.mockResolvedValue(undefined);

            const token = await authRepository.getValidToken();
            expect(token).toBeNull();
        });

        it('should check if token needs refresh', async () => {
            const refreshNeededAuthData = {
                ...mockAuthData,
                tokenMetadata: {
                    ...mockTokenMetadata,
                    refreshAfter: Math.floor(Date.now() / 1000) - 300, // 5 minutes ago
                    expiresAt: Math.floor(Date.now() / 1000) + 1800 // 30 minutes from now
                }
            };

            mockStorage.getItem.mockResolvedValue(refreshNeededAuthData);

            const shouldRefresh = await authRepository.shouldRefreshToken();
            expect(shouldRefresh).toBe(true);
        });

        it('should clear auth data', async () => {
            mockStorage.removeItem.mockResolvedValue(undefined);

            await authRepository.clearAuthData();
            expect(mockStorage.removeItem).toHaveBeenCalledWith('user_auth_data');
        });

        it('should check authentication status', async () => {
            mockStorage.getItem.mockResolvedValue(mockAuthData);

            const isAuthenticated = await authRepository.isAuthenticated();
            expect(isAuthenticated).toBe(true);
        });

        it('should handle storage errors gracefully', async () => {
            const error = new Error('Storage error');
            mockStorage.getItem.mockRejectedValue(error);

            const result = await authRepository.getAuthData();
            expect(result).toBeNull();

            const token = await authRepository.getValidToken();
            expect(token).toBeNull();

            const isAuthenticated = await authRepository.isAuthenticated();
            expect(isAuthenticated).toBe(false);
        });
    });

    describe('Admin Roles', () => {
        it('should save and retrieve admin roles', async () => {
            // First call for saveAdminRoles (to get existing auth data)
            mockStorage.getItem.mockResolvedValueOnce(mockAuthData);
            // Second call for setItem (saveAuthData)
            mockStorage.setItem.mockResolvedValue(undefined);
            // Third call for getAdminRoles
            const authDataWithAdminRoles = {
                ...mockAuthData,
                adminRoles: mockAdminRoles
            };
            mockStorage.getItem.mockResolvedValueOnce(authDataWithAdminRoles);

            await authRepository.saveAdminRoles(mockAdminRoles);

            const expectedUpdatedAuthData = {
                ...mockAuthData,
                adminRoles: mockAdminRoles
            };

            expect(mockStorage.setItem).toHaveBeenCalledWith('user_auth_data', expectedUpdatedAuthData);

            const adminRoles = await authRepository.getAdminRoles();
            expect(adminRoles).toEqual(mockAdminRoles);
        });

        it('should get auth status with admin roles', async () => {
            const authDataWithAdminRoles = {
                ...mockAuthData,
                adminRoles: mockAdminRoles
            };

            mockStorage.getItem.mockResolvedValue(authDataWithAdminRoles);

            const authStatus = await authRepository.getAuthStatus();
            expect(authStatus.isAuthenticated).toBe(true);
            expect(authStatus.hasAdminRoles).toBe(true);
            expect(authStatus.tokenExpiresIn).toBeGreaterThan(0);
        });
    });

    describe('Edge Cases', () => {
        it('should handle null auth data', async () => {
            mockStorage.getItem.mockResolvedValue(null);

            const authData = await authRepository.getAuthData();
            expect(authData).toBeNull();

            const userDetails = await authRepository.getUserDetails();
            expect(userDetails).toBeNull();

            const tokenMetadata = await authRepository.getTokenMetadata();
            expect(tokenMetadata).toBeNull();
        });

        it('should handle malformed auth data', async () => {
            const malformedData = { invalid: 'data' };
            mockStorage.getItem.mockResolvedValue(malformedData);

            const token = await authRepository.getValidToken();
            expect(token).toBeNull();
        });
    });
});
